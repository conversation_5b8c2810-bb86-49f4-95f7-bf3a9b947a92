package com.curefit.odin.utils;

import com.curefit.odin.admin.models.Category;
import com.curefit.odin.admin.models.SubCategory;
import com.curefit.odin.admin.models.Tenant;
import com.curefit.odin.admin.pojo.CategoryEntry;
import com.curefit.odin.admin.pojo.DefaultAssigneeEntry;
import com.curefit.odin.admin.pojo.UserEntry;
import com.curefit.odin.enums.Priority;
import com.curefit.odin.enums.Status;
import com.curefit.odin.enums.TicketDest;
import com.curefit.odin.enums.TicketSource;
import com.curefit.odin.sprinklr.models.SprinklrTicket;
import com.curefit.odin.sprinklr.pojo.SprinklrTicketEntry;
import com.curefit.odin.user.models.Ticket;
import com.curefit.odin.user.pojo.TicketEntry;
import com.curefit.odin.utils.pojo.LocationEntry;

import java.util.*;

/**
 * Utility class for generating test POJOs and entities
 */
public class PojoGenerator {

    /**
     * Creates a test TicketEntry with default values
     */
    public static TicketEntry createTestTicketEntry() {
        TicketEntry ticketEntry = new TicketEntry();
        ticketEntry.setId(1L);
        ticketEntry.setTitle("Test Ticket");
        ticketEntry.setDescription("Test Description");
        ticketEntry.setPriority(Priority.P5);
        ticketEntry.setStatus(Status.OPEN);
        ticketEntry.setSource(TicketSource.ODIN);
        ticketEntry.setDest(TicketDest.CUSTOMER_SUPPORT);
        ticketEntry.setCategoryId(1L);
        ticketEntry.setSubCategoryId(1L);
        ticketEntry.setTenantId(1L);
        ticketEntry.setCreatedBy("<EMAIL>");
        ticketEntry.setCreatedOn(new Date());
        ticketEntry.setLocationEntry(createTestLocationEntry());
        ticketEntry.setLabels(new HashSet<>());
        return ticketEntry;
    }

    /**
     * Creates a test Ticket entity with default values
     */
    public static Ticket createTestTicket() {
        Ticket ticket = new Ticket();
        ticket.setId(1L);
        ticket.setTitle("Test Ticket");
        ticket.setDescription("Test Description");
        ticket.setPriority(Priority.P5);
        ticket.setStatus(Status.OPEN);
        ticket.setSource(TicketSource.ODIN);
        ticket.setDest(TicketDest.CUSTOMER_SUPPORT);
        ticket.setCreatedBy("<EMAIL>");
        ticket.setCreatedOn(new Date());
        return ticket;
    }

    /**
     * Creates a test SprinklrTicketEntry with default values
     */
    public static SprinklrTicketEntry createTestSprinklrTicketEntry() {
        SprinklrTicketEntry entry = SprinklrTicketEntry.builder()
                .userId("test-user-id")
                .caseId("CASE123")
                .caseNumber("12345")
                .subject("Test Subject")
                .description("Test Description")
                .status("Open")
                .customProperties(createSampleCustomProperties())
                .isTicket(true)
                .odinTicketId("100")
                .email("<EMAIL>")
                .conversationId("conv123")
                .firstMessageId("msg123")
                .build();
        entry.setId(1L);
        return entry;
    }

    /**
     * Creates a test SprinklrTicket entity with default values
     */
    public static SprinklrTicket createTestSprinklrTicket() {
        SprinklrTicket sprinklrTicket = new SprinklrTicket();
        sprinklrTicket.setId(1L);
        sprinklrTicket.setUserId("test-user-id");
        sprinklrTicket.setCaseId("CASE123");
        sprinklrTicket.setCaseNumber("12345");
        sprinklrTicket.setSubject("Test Subject");
        sprinklrTicket.setDescription("Test Description");
        sprinklrTicket.setStatus("Open");
        sprinklrTicket.setCustomProperties(createSampleCustomProperties());
        return sprinklrTicket;
    }

    /**
     * Creates a test UserEntry with default values
     */
    public static UserEntry createTestUserEntry() {
        UserEntry userEntry = new UserEntry();
        userEntry.setId(1L);
        userEntry.setEmailId("<EMAIL>");
        userEntry.setName("Test User");
        userEntry.setActive(true);
        return userEntry;
    }

    /**
     * Creates a test CategoryEntry with default values
     */
    public static CategoryEntry createTestCategoryEntry() {
        CategoryEntry categoryEntry = new CategoryEntry();
        categoryEntry.setId(1L);
        categoryEntry.setName("Test Category");
        categoryEntry.setTenantId(1L);
        categoryEntry.setActive(true);
        return categoryEntry;
    }

    /**
     * Creates a test LocationEntry with default values
     */
    public static LocationEntry createTestLocationEntry() {
        LocationEntry locationEntry = new LocationEntry();
        locationEntry.setId(1L);
        locationEntry.setCenterName("Test Location");
        locationEntry.setActive(true);
        return locationEntry;
    }

    /**
     * Creates a test Tenant entity with default values
     */
    public static Tenant createTestTenant() {
        Tenant tenant = new Tenant();
        tenant.setId(1L);
        tenant.setName("Test Tenant");
        tenant.setActive(true);
        return tenant;
    }

    /**
     * Creates a test Category entity with default values
     */
    public static Category createTestCategory() {
        Category category = new Category();
        category.setId(1L);
        category.setName("Test Category");
        category.setActive(true);
        category.setMaxStatusChangeTime(24); // 24 hours
        return category;
    }

    /**
     * Creates a test SubCategory entity with default values
     */
    public static SubCategory createTestSubCategory() {
        SubCategory subCategory = new SubCategory();
        subCategory.setId(1L);
        subCategory.setName("Test SubCategory");
        subCategory.setActive(true);
        return subCategory;
    }

    /**
     * Creates a test DefaultAssigneeEntry with default values
     */
    public static DefaultAssigneeEntry createTestDefaultAssigneeEntry() {
        DefaultAssigneeEntry defaultAssigneeEntry = new DefaultAssigneeEntry();
        defaultAssigneeEntry.setId(1L);
        defaultAssigneeEntry.setCategoryId(1L);
        defaultAssigneeEntry.setSubCategoryId(1L);
        return defaultAssigneeEntry;
    }

    /**
     * Creates sample custom properties for testing
     */
    public static Map<String, List<Object>> createSampleCustomProperties() {
        Map<String, List<Object>> properties = new HashMap<>();
        properties.put("priority", Arrays.asList("High"));
        properties.put("assignee", Arrays.asList("test.user"));
        properties.put("department", Arrays.asList("IT"));
        return properties;
    }

    /**
     * Creates an empty custom properties map
     */
    public static Map<String, List<Object>> createEmptyCustomProperties() {
        return new HashMap<>();
    }

    /**
     * Creates a list of test Status values for status transitions
     */
    public static List<Status> createTestStatusList() {
        return Arrays.asList(Status.IN_PROGRESS, Status.RESOLVED);
    }

    /**
     * Creates a list of test Long values
     */
    public static List<Long> createTestLongList() {
        return Arrays.asList(1L, 2L, 3L);
    }

    /**
     * Creates a set of test String values
     */
    public static Set<String> createTestStringSet() {
        return new HashSet<>(Arrays.asList("test1", "test2", "test3"));
    }

    /**
     * Creates an empty list
     */
    public static <T> List<T> createEmptyList() {
        return new ArrayList<>();
    }

    /**
     * Creates an empty set
     */
    public static <T> Set<T> createEmptySet() {
        return new HashSet<>();
    }

    /**
     * Creates an empty map
     */
    public static <K, V> Map<K, V> createEmptyMap() {
        return new HashMap<>();
    }
}
