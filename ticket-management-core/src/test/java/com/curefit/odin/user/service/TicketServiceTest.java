package com.curefit.odin.user.service;

import com.curefit.common.data.exception.BaseException;
import com.curefit.common.data.exception.ResourceNotFoundException;
import com.curefit.commons.sf.audit.InMemoryChangeEventPersistence;
import com.curefit.commons.sf.crypto.EntityCryptoUtils;
import com.curefit.commons.sf.util.MetricsUtils;
import com.curefit.configstore.sdk.AppConfigCache;
import com.curefit.odin.admin.pojo.CategoryEntry;
import com.curefit.odin.admin.pojo.UserEntry;
import com.curefit.odin.admin.service.CategoryService;
import com.curefit.odin.admin.service.SubCategoryService;
import com.curefit.odin.admin.service.UserService;
import com.curefit.odin.admin.service.CustomFieldService;
import com.curefit.odin.admin.service.IssueTemplateService;
import com.curefit.odin.admin.service.TicketApprovalConfigService;
import com.curefit.odin.admin.service.TenantService;
import com.curefit.odin.admin.service.SLAService;
import com.curefit.odin.admin.service.DefaultAssigneeService;
import com.curefit.odin.enums.Priority;
import com.curefit.odin.enums.Status;
import com.curefit.odin.enums.TicketDest;
import com.curefit.odin.enums.TicketSource;
import com.curefit.odin.utils.PojoGenerator;
import com.curefit.odin.user.models.Ticket;
import com.curefit.odin.user.pojo.TicketEntry;
import com.curefit.odin.user.repositories.TicketDAO;
import com.curefit.odin.utils.AuthService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.lang.reflect.Field;
import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class TicketServiceTest {

    @Mock
    private TicketDAO ticketDAO;

    @Mock
    private FieldDataService fieldDataService;

    @Mock
    private CategoryService categoryService;

    @Mock
    private SubCategoryService subCategoryService;

    @Mock
    private TicketWatcherService ticketWatcherService;

    @Mock
    private AttachmentService attachmentService;

    @Mock
    private CommentService commentService;

    @Mock
    private UserService userService;

    @Mock
    private CustomFieldService customFieldService;

    @Mock
    private IssueTemplateService issueTemplateService;

    @Mock
    private TicketApprovalConfigService ticketApprovalConfigService;

    @Mock
    private AuthService authService;

    @Mock
    private AppConfigCache appConfigCache;

    @Mock
    private MetricsUtils metricsUtils;

    @Mock
    private EntityCryptoUtils entityCryptoUtils;

    @Mock
    private InMemoryChangeEventPersistence inMemoryChangeEventPersistence;

    @Mock
    private TenantService tenantService;

    @Mock
    private SLAService slaService;

    @Mock
    private DefaultAssigneeService defaultAssigneeService;

    @InjectMocks
    private TicketService ticketService;

    private TicketEntry testTicketEntry;
    private Ticket testTicket;
    private UserEntry testUser;
    private CategoryEntry testCategory;

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
        injectBaseMySQLServiceDependencies();
        setupTestData();
    }

    private void injectBaseMySQLServiceDependencies() throws Exception {
        // Inject MetricsUtils into the parent BaseMySQLService
        Field metricsUtilsField = ticketService.getClass().getSuperclass().getDeclaredField("metricsUtils");
        metricsUtilsField.setAccessible(true);
        metricsUtilsField.set(ticketService, metricsUtils);

        // Inject EntityCryptoUtils into the parent BaseMySQLService
        Field entityCryptoUtilsField = ticketService.getClass().getSuperclass().getDeclaredField("entityCryptoUtils");
        entityCryptoUtilsField.setAccessible(true);
        entityCryptoUtilsField.set(ticketService, entityCryptoUtils);

        // Inject InMemoryChangeEventPersistence into the parent BaseMySQLService
        Field inMemoryChangeEventPersistenceField = ticketService.getClass().getSuperclass().getDeclaredField("inMemoryChangeEventPersistence");
        inMemoryChangeEventPersistenceField.setAccessible(true);
        inMemoryChangeEventPersistenceField.set(ticketService, inMemoryChangeEventPersistence);

        // Mock the metricsUtils behavior
        when(metricsUtils.getMetricsKey(anyString())).thenReturn("test-metrics-key");

        // Mock the entityCryptoUtils behavior
        doNothing().when(entityCryptoUtils).encrypt(any(), any());
        doNothing().when(entityCryptoUtils).decrypt(any(), any());

        // Mock the inMemoryChangeEventPersistence behavior
        doNothing().when(inMemoryChangeEventPersistence).createAndPersistChangeEvent(any(), any());
    }

    private void setupTestData() {
        // Setup test user
        testUser = new UserEntry();
        testUser.setEmailId("<EMAIL>");
        testUser.setName("Test User");

        // Setup test category
        testCategory = new CategoryEntry();
        testCategory.setId(1L);
        testCategory.setName("Test Category");
        testCategory.setTenantId(1L);

        // Setup test ticket entry
        testTicketEntry = new TicketEntry();
        testTicketEntry.setId(1L);
        testTicketEntry.setTitle("Test Ticket");
        testTicketEntry.setDescription("Test Description");
        testTicketEntry.setStatus(Status.OPEN);
        testTicketEntry.setPriority(Priority.P5);
        testTicketEntry.setTenantId(1L);
        testTicketEntry.setCategoryId(1L);
        testTicketEntry.setSubCategoryId(1L);
        testTicketEntry.setReporter(testUser);
        testTicketEntry.setSource(TicketSource.ODIN);
        testTicketEntry.setDest(TicketDest.CUSTOMER_SUPPORT);
        testTicketEntry.setUserId("test-user-id");

        // Setup test ticket model
        testTicket = new Ticket();
        testTicket.setId(1L);
        testTicket.setTitle("Test Ticket");
        testTicket.setDescription("Test Description");
        testTicket.setStatus(Status.OPEN);
        testTicket.setPriority(Priority.P5);
        testTicket.setCreatedBy("<EMAIL>");
        testTicket.setCreatedOn(new Date());
    }

    @Test
    public void testCreate_Success() throws BaseException {
        // Arrange
        doNothing().when(fieldDataService).validate(any(TicketEntry.class));
        when(ticketDAO.save(any(Ticket.class))).thenReturn(testTicket);
        when(fieldDataService.findFieldDataByTicketId(anyLong())).thenReturn(new ArrayList<>());
        when(ticketWatcherService.findByTicketId(anyLong())).thenReturn(new ArrayList<>());
        when(customFieldService.filterActiveV2(any())).thenReturn(new ArrayList<>());
        when(ticketApprovalConfigService.fetchTicketApprovers(any())).thenReturn(new HashSet<>());
        when(tenantService.fetchEntityById(anyLong())).thenReturn(new com.curefit.odin.admin.models.Tenant());
        when(slaService.getDueDate(anyLong(), anyLong(), anyLong(), any())).thenReturn(System.currentTimeMillis() + 86400000L);
        when(defaultAssigneeService.fetchDefaultAssignee(anyLong(), anyLong())).thenReturn(null);

        // Act
        TicketEntry result = ticketService.create(testTicketEntry);

        // Assert
        assertNotNull(result);
        assertEquals(testTicketEntry.getTitle(), result.getTitle());
        assertEquals(testTicketEntry.getDescription(), result.getDescription());
        verify(fieldDataService).validate(testTicketEntry);
        verify(ticketDAO).save(any(Ticket.class));
    }

    @Test(expected = BaseException.class)
    public void testCreate_ValidationFailure() throws BaseException {
        // Arrange
        doThrow(new BaseException("Validation failed")).when(fieldDataService).validate(any(TicketEntry.class));

        // Act
        ticketService.create(testTicketEntry);
    }

    @Test
    public void testFindOneById_Success() throws BaseException {
        // Arrange
        testTicket.setId(1L); // Ensure the ticket has the ID we're looking for
        when(ticketDAO.findById(1L)).thenReturn(Optional.of(testTicket));
        when(fieldDataService.findFieldDataByTicketId(anyLong())).thenReturn(new ArrayList<>());
        when(ticketWatcherService.findByTicketId(anyLong())).thenReturn(new ArrayList<>());
        when(customFieldService.filterActiveV2(any())).thenReturn(new ArrayList<>());
        when(ticketApprovalConfigService.fetchTicketApprovers(any())).thenReturn(new HashSet<>());
        doNothing().when(authService).checkAuthorized(any(TicketEntry.class), any());

        // Act
        TicketEntry result = ticketService.findOneById(1L);

        // Assert
        assertNotNull(result);
        assertEquals(testTicket.getId(), result.getId());
        assertEquals(testTicket.getTitle(), result.getTitle());
        verify(authService).checkAuthorized(any(TicketEntry.class), any());
    }

    @Test(expected = ResourceNotFoundException.class)
    public void testFindOneById_NotFound() throws BaseException {
        // Arrange
        when(ticketDAO.findById(1L)).thenReturn(Optional.empty());

        // Act
        ticketService.findOneById(1L);
    }

    @Test
    public void testUpdateStatus_Success() throws BaseException {
        // Arrange
        testTicket.setId(1L);
        Status newStatus = Status.RESOLVED;
        when(ticketDAO.findById(1L)).thenReturn(Optional.of(testTicket));
        when(ticketDAO.save(any(Ticket.class))).thenReturn(testTicket);
        when(fieldDataService.findFieldDataByTicketId(anyLong())).thenReturn(new ArrayList<>());
        when(ticketWatcherService.findByTicketId(anyLong())).thenReturn(new ArrayList<>());
        when(customFieldService.filterActiveV2(any())).thenReturn(new ArrayList<>());
        when(ticketApprovalConfigService.fetchTicketApprovers(any())).thenReturn(new HashSet<>());

        // Act
        TicketEntry result = ticketService.updateStatus(1L, newStatus);

        // Assert
        assertNotNull(result);
        assertEquals(newStatus, result.getStatus());
        verify(ticketDAO).save(any(Ticket.class));
    }

    @Test
    public void testUpdateStatus_SameStatus() throws BaseException {
        // Arrange
        testTicket.setId(1L);
        Status currentStatus = Status.OPEN;
        testTicket.setStatus(currentStatus);
        when(ticketDAO.findById(1L)).thenReturn(Optional.of(testTicket));
        when(fieldDataService.findFieldDataByTicketId(anyLong())).thenReturn(new ArrayList<>());
        when(ticketWatcherService.findByTicketId(anyLong())).thenReturn(new ArrayList<>());
        when(customFieldService.filterActiveV2(any())).thenReturn(new ArrayList<>());
        when(ticketApprovalConfigService.fetchTicketApprovers(any())).thenReturn(new HashSet<>());

        // Act
        TicketEntry result = ticketService.updateStatus(1L, currentStatus);

        // Assert
        assertNotNull(result);
        assertEquals(currentStatus, result.getStatus());
        verify(ticketDAO, never()).save(any(Ticket.class));
    }

    @Test
    public void testUpdateAssignees_Success() throws BaseException {
        // Arrange
        testTicket.setId(1L);
        List<String> userIds = Arrays.asList("<EMAIL>", "<EMAIL>");
        when(ticketDAO.findById(1L)).thenReturn(Optional.of(testTicket));
        when(ticketDAO.save(any(Ticket.class))).thenReturn(testTicket);
        when(fieldDataService.findFieldDataByTicketId(anyLong())).thenReturn(new ArrayList<>());
        when(ticketWatcherService.findByTicketId(anyLong())).thenReturn(new ArrayList<>());
        when(customFieldService.filterActiveV2(any())).thenReturn(new ArrayList<>());
        when(ticketApprovalConfigService.fetchTicketApprovers(any())).thenReturn(new HashSet<>());

        // Act
        TicketEntry result = ticketService.updateAssignees(1L, userIds);

        // Assert
        assertNotNull(result);
        verify(ticketDAO).save(any(Ticket.class));
    }

    @Test
    public void testAddLabelByTicketId_Success() throws BaseException {
        // Arrange
        testTicket.setId(1L);
        String label = "urgent";
        testTicketEntry.setLabels(new HashSet<>());
        when(ticketDAO.findById(1L)).thenReturn(Optional.of(testTicket));
        when(ticketDAO.save(any(Ticket.class))).thenReturn(testTicket);
        when(fieldDataService.findFieldDataByTicketId(anyLong())).thenReturn(new ArrayList<>());
        when(ticketWatcherService.findByTicketId(anyLong())).thenReturn(new ArrayList<>());
        when(customFieldService.filterActiveV2(any())).thenReturn(new ArrayList<>());
        when(ticketApprovalConfigService.fetchTicketApprovers(any())).thenReturn(new HashSet<>());

        // Act
        TicketEntry result = ticketService.addLabelByTicketId(1L, label);

        // Assert
        assertNotNull(result);
        assertTrue(result.getLabels().contains(label));
    }

    @Test
    public void testFetchDetail_Success() throws BaseException {
        // Arrange
        testTicket.setId(1L);
        when(ticketDAO.findById(1L)).thenReturn(Optional.of(testTicket));
        when(fieldDataService.findFieldDataByTicketId(anyLong())).thenReturn(new ArrayList<>());
        when(ticketWatcherService.findByTicketId(anyLong())).thenReturn(new ArrayList<>());
        when(customFieldService.filterActiveV2(any())).thenReturn(new ArrayList<>());
        when(ticketApprovalConfigService.fetchTicketApprovers(any())).thenReturn(new HashSet<>());

        // Act
        TicketEntry result = ticketService.fetchDetail(1L);

        // Assert
        assertNotNull(result);
        assertEquals(testTicket.getId(), result.getId());
    }

    @Test
    public void testRateTicketResolution_Success() throws BaseException {
        // Arrange
        testTicket.setId(1L);
        Integer rating = 5;
        testTicket.setStatus(Status.RESOLVED);
        when(ticketDAO.findById(1L)).thenReturn(Optional.of(testTicket));
        when(ticketDAO.save(any(Ticket.class))).thenReturn(testTicket);
        when(fieldDataService.findFieldDataByTicketId(anyLong())).thenReturn(new ArrayList<>());
        when(ticketWatcherService.findByTicketId(anyLong())).thenReturn(new ArrayList<>());
        when(customFieldService.filterActiveV2(any())).thenReturn(new ArrayList<>());
        when(ticketApprovalConfigService.fetchTicketApprovers(any())).thenReturn(new HashSet<>());

        // Act
        TicketEntry result = ticketService.rateTicketResolution(1L, rating);

        // Assert
        assertNotNull(result);
        assertEquals(rating, result.getResolutionRating());
    }

    @Test(expected = BaseException.class)
    public void testRateTicketResolution_TicketNotResolved() throws BaseException {
        // Arrange
        Integer rating = 5;
        testTicket.setStatus(Status.OPEN);
        when(ticketDAO.findById(1L)).thenReturn(Optional.of(testTicket));
        when(fieldDataService.findFieldDataByTicketId(anyLong())).thenReturn(new ArrayList<>());
        when(ticketWatcherService.findByTicketId(anyLong())).thenReturn(new ArrayList<>());
        when(customFieldService.filterActiveV2(any())).thenReturn(new ArrayList<>());
        when(ticketApprovalConfigService.fetchTicketApprovers(any())).thenReturn(new HashSet<>());

        // Act
        ticketService.rateTicketResolution(1L, rating);
    }

    @Test
    public void testFindTicketById_Success() throws BaseException {
        // Arrange
        testTicket.setId(1L);
        when(ticketDAO.findById(1L)).thenReturn(Optional.of(testTicket));
        when(fieldDataService.findFieldDataByTicketId(anyLong())).thenReturn(new ArrayList<>());
        when(ticketWatcherService.findByTicketId(anyLong())).thenReturn(new ArrayList<>());
        when(customFieldService.filterActiveV2(any())).thenReturn(new ArrayList<>());
        when(ticketApprovalConfigService.fetchTicketApprovers(any())).thenReturn(new HashSet<>());

        // Act
        TicketEntry result = ticketService.findTicketById(1L);

        // Assert
        assertNotNull(result);
        assertEquals(testTicket.getId(), result.getId());
    }

    @Test
    public void testFindTicketId_Success() {
        // Act
        Long result = ticketService.findTicketId(testTicketEntry);

        // Assert
        assertEquals(testTicketEntry.getId(), result);
    }

    @Test
    public void testFindTicketIdById_Success() {
        // Act
        Long result = ticketService.findTicketIdById(1L);

        // Assert
        assertEquals(Long.valueOf(1L), result);
    }

    @Test
    public void testMarkSLAReminderSent_Success() throws BaseException {
        // Arrange
        testTicket.setId(1L);
        when(ticketDAO.findById(1L)).thenReturn(Optional.of(testTicket));
        when(ticketDAO.save(any(Ticket.class))).thenReturn(testTicket);
        doNothing().when(authService).checkAuthorized(anyLong(), any());

        // Act
        ticketService.markSLAReminderSent(1L);

        // Assert
        verify(ticketDAO).save(any(Ticket.class));
    }

    @Test
    public void testPatchUpdate_Success() throws BaseException {
        // Arrange
        testTicket.setId(1L);
        TicketEntry updateEntry = new TicketEntry();
        updateEntry.setTitle("Updated Title");
        updateEntry.setDescription("Updated Description");
        updateEntry.setPriority(Priority.P2);

        when(ticketDAO.findById(1L)).thenReturn(Optional.of(testTicket));
        when(ticketDAO.save(any(Ticket.class))).thenReturn(testTicket);
        when(fieldDataService.findFieldDataByTicketId(anyLong())).thenReturn(new ArrayList<>());
        doNothing().when(authService).checkAuthorized(anyLong(), any());

        // Act
        TicketEntry result = ticketService.patchUpdate(1L, updateEntry);

        // Assert
        assertNotNull(result);
        verify(ticketDAO).save(any(Ticket.class));
    }

    @Test
    public void testFindNextStatus_Success() throws BaseException, java.io.IOException {
        // Arrange
        testTicket.setId(1L);
        when(ticketDAO.findById(1L)).thenReturn(Optional.of(testTicket));
        when(fieldDataService.findFieldDataByTicketId(anyLong())).thenReturn(new ArrayList<>());
        when(ticketWatcherService.findByTicketId(anyLong())).thenReturn(new ArrayList<>());
        when(customFieldService.filterActiveV2(any())).thenReturn(new ArrayList<>());
        when(ticketApprovalConfigService.fetchTicketApprovers(any())).thenReturn(new HashSet<>());

        // Mock the appConfigCache to return a List for REJECTED_BLACKLISTED_CATEGORIES
        List<Long> rejectedBlacklistCategories = new ArrayList<>();
        when(appConfigCache.getConfig(eq("REJECTED_BLACKLISTED_CATEGORIES"), any(), any())).thenReturn(rejectedBlacklistCategories);

        // Mock other config calls that might be needed
        when(appConfigCache.getConfig(anyString(), any(), any())).thenReturn(new ArrayList<>());

        // Act
        List<Status> result = ticketService.findNextStatus(1L);

        // Assert
        assertNotNull(result);
        assertFalse(result.isEmpty());
    }
}
