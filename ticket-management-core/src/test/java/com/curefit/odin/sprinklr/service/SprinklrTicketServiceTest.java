package com.curefit.odin.sprinklr.service;

import com.curefit.common.data.exception.BaseException;
import com.curefit.common.data.exception.ResourceNotFoundException;
import com.curefit.commons.sf.audit.InMemoryChangeEventPersistence;
import com.curefit.commons.sf.crypto.EntityCryptoUtils;
import com.curefit.commons.sf.util.MetricsUtils;
import com.curefit.odin.sprinklr.models.SprinklrTicket;
import com.curefit.odin.sprinklr.pojo.SprinklrTicketEntry;
import com.curefit.odin.sprinklr.repositories.SprinklrTicketRepository;
import com.curefit.odin.user.service.TicketService;
import com.curefit.odin.user.service.CommentService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class SprinklrTicketServiceTest {

    @Mock
    private SprinklrTicketRepository sprinklrTicketRepository;

    @Mock
    private SprinklrCultCustomFieldMappingService sprinklrCultCustomFieldMappingService;

    @Mock
    private SprinklrCultConfigurationService sprinklrCultConfigurationService;

    @Mock
    private TicketService ticketService;

    @Mock
    private CommentService commentService;

    @Mock
    private MetricsUtils metricsUtils;

    @Mock
    private EntityCryptoUtils entityCryptoUtils;

    @Mock
    private InMemoryChangeEventPersistence inMemoryChangeEventPersistence;

    @InjectMocks
    private SprinklrTicketService sprinklrTicketService;

    private SprinklrTicketEntry testSprinklrTicketEntry;
    private SprinklrTicket testSprinklrTicket;
    private Method mergeCustomPropertiesMethod;

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
        injectBaseMySQLServiceDependencies();
        setupTestData();
        setupReflection();
    }

    private void injectBaseMySQLServiceDependencies() throws Exception {
        // Inject MetricsUtils into the parent BaseMySQLService
        Field metricsUtilsField = sprinklrTicketService.getClass().getSuperclass().getDeclaredField("metricsUtils");
        metricsUtilsField.setAccessible(true);
        metricsUtilsField.set(sprinklrTicketService, metricsUtils);

        // Inject EntityCryptoUtils into the parent BaseMySQLService
        Field entityCryptoUtilsField = sprinklrTicketService.getClass().getSuperclass().getDeclaredField("entityCryptoUtils");
        entityCryptoUtilsField.setAccessible(true);
        entityCryptoUtilsField.set(sprinklrTicketService, entityCryptoUtils);

        // Mock the metricsUtils behavior
        when(metricsUtils.getMetricsKey(anyString())).thenReturn("test-metrics-key");

        // Mock the entityCryptoUtils behavior
        doNothing().when(entityCryptoUtils).encrypt(any(), any());
        doNothing().when(entityCryptoUtils).decrypt(any(), any());

        // Inject InMemoryChangeEventPersistence into the parent BaseMySQLService
        Field inMemoryChangeEventPersistenceField = sprinklrTicketService.getClass().getSuperclass().getDeclaredField("inMemoryChangeEventPersistence");
        inMemoryChangeEventPersistenceField.setAccessible(true);
        inMemoryChangeEventPersistenceField.set(sprinklrTicketService, inMemoryChangeEventPersistence);

        // Mock the inMemoryChangeEventPersistence behavior
        doNothing().when(inMemoryChangeEventPersistence).createAndPersistChangeEvent(any(), any());
    }

    private void setupTestData() {
        // Setup test SprinklrTicketEntry
        testSprinklrTicketEntry = SprinklrTicketEntry.builder()
                .userId("test-user-id")
                .caseId("CASE123")
                .caseNumber("12345")
                .subject("Test Subject")
                .description("Test Description")
                .status("Open")
                .customProperties(createSampleCustomProperties())
                .isTicket(true)
                .odinTicketId("100")
                .email("<EMAIL>")
                .conversationId("conv123")
                .firstMessageId("msg123")
                .build();

        // Setup test SprinklrTicket model
        testSprinklrTicket = new SprinklrTicket();
        testSprinklrTicket.setId(1L);
        testSprinklrTicket.setUserId("test-user-id");
        testSprinklrTicket.setCaseId("CASE123");
        testSprinklrTicket.setCaseNumber("12345");
        testSprinklrTicket.setSubject("Test Subject");
        testSprinklrTicket.setDescription("Test Description");
        testSprinklrTicket.setStatus("Open");
        testSprinklrTicket.setCustomProperties(createSampleCustomProperties());
    }

    private void setupReflection() throws Exception {
        // Get the private method using reflection
        mergeCustomPropertiesMethod = SprinklrTicketService.class.getDeclaredMethod(
                "mergeCustomProperties", SprinklrTicketEntry.class, SprinklrTicketEntry.class);
        mergeCustomPropertiesMethod.setAccessible(true);
    }

    private Map<String, List<Object>> createSampleCustomProperties() {
        Map<String, List<Object>> properties = new HashMap<>();
        properties.put("status", Arrays.asList("OPEN"));
        properties.put("priority", Arrays.asList("MEDIUM"));
        properties.put("assignee", Arrays.asList("test.user"));
        return properties;
    }

    @Test
    public void testCreate_Success() throws BaseException {
        // Arrange
        testSprinklrTicket.setId(1L);
        testSprinklrTicketEntry.setId(1L);
        when(sprinklrTicketRepository.save(any(SprinklrTicket.class))).thenReturn(testSprinklrTicket);

        // Act
        SprinklrTicketEntry result = sprinklrTicketService.create(testSprinklrTicketEntry);

        // Assert
        assertNotNull(result);
        assertEquals(testSprinklrTicketEntry.getCaseNumber(), result.getCaseNumber());
        assertEquals(testSprinklrTicketEntry.getSubject(), result.getSubject());
        verify(sprinklrTicketRepository).save(any(SprinklrTicket.class));
    }

    @Test
    public void testFindOneById_Success() throws BaseException {
        // Arrange
        testSprinklrTicket.setId(1L);
        when(sprinklrTicketRepository.findById(1L)).thenReturn(Optional.of(testSprinklrTicket));

        // Act
        SprinklrTicketEntry result = sprinklrTicketService.findOneById(1L);

        // Assert
        assertNotNull(result);
        assertEquals(testSprinklrTicket.getId(), result.getId());
        assertEquals(testSprinklrTicket.getCaseNumber(), result.getCaseNumber());
    }

    @Test(expected = ResourceNotFoundException.class)
    public void testFindOneById_NotFound() throws BaseException {
        // Arrange
        when(sprinklrTicketRepository.findById(1L)).thenReturn(Optional.empty());

        // Act
        sprinklrTicketService.findOneById(1L);
    }



    @Test
    public void testPatchUpdate_Success() throws BaseException {
        // Arrange
        testSprinklrTicket.setId(1L);
        SprinklrTicketEntry updateEntry = SprinklrTicketEntry.builder()
                .status("Resolved")
                .customProperties(createSampleCustomProperties())
                .build();

        when(sprinklrTicketRepository.findById(1L)).thenReturn(Optional.of(testSprinklrTicket));
        when(sprinklrTicketRepository.save(any(SprinklrTicket.class))).thenReturn(testSprinklrTicket);

        // Act
        SprinklrTicketEntry result = sprinklrTicketService.patchUpdate(1L, updateEntry);

        // Assert
        assertNotNull(result);
        verify(sprinklrTicketRepository).save(any(SprinklrTicket.class));
    }



    @Test
    public void testConvertToEntry_Success() {
        // Act
        SprinklrTicketEntry result = sprinklrTicketService.convertToEntry(testSprinklrTicket);

        // Assert
        assertNotNull(result);
        assertEquals(testSprinklrTicket.getId(), result.getId());
        assertEquals(testSprinklrTicket.getCaseNumber(), result.getCaseNumber());
        assertEquals(testSprinklrTicket.getSubject(), result.getSubject());
        assertEquals(testSprinklrTicket.getStatus(), result.getStatus());
    }

    @Test
    public void testConvertToEntity_Success() {
        // Act
        SprinklrTicket result = sprinklrTicketService.convertToEntity(testSprinklrTicketEntry);

        // Assert
        assertNotNull(result);
        assertEquals(testSprinklrTicketEntry.getId(), result.getId());
        assertEquals(testSprinklrTicketEntry.getCaseNumber(), result.getCaseNumber());
        assertEquals(testSprinklrTicketEntry.getSubject(), result.getSubject());
        assertEquals(testSprinklrTicketEntry.getStatus(), result.getStatus());
    }

    // Test for mergeCustomProperties method using reflection
    @Test
    public void testMergeCustomProperties_ExistingCustomPropertiesNull() throws Exception {
        // Arrange
        SprinklrTicketEntry newEntry = createSprinklrTicketEntry();
        SprinklrTicketEntry existingEntry = createSprinklrTicketEntry();
        
        Map<String, List<Object>> newProperties = createSampleCustomProperties();
        newEntry.setCustomProperties(newProperties);
        existingEntry.setCustomProperties(null);
        
        // Act
        mergeCustomPropertiesMethod.invoke(sprinklrTicketService, newEntry, existingEntry);
        
        // Assert
        assertEquals(newProperties, newEntry.getCustomProperties());
    }

    @Test
    public void testMergeCustomProperties_NewCustomPropertiesNull() throws Exception {
        // Arrange
        SprinklrTicketEntry newEntry = createSprinklrTicketEntry();
        SprinklrTicketEntry existingEntry = createSprinklrTicketEntry();
        
        Map<String, List<Object>> existingProperties = createSampleCustomProperties();
        newEntry.setCustomProperties(null);
        existingEntry.setCustomProperties(existingProperties);
        
        // Act
        mergeCustomPropertiesMethod.invoke(sprinklrTicketService, newEntry, existingEntry);
        
        // Assert
        assertEquals(existingProperties, newEntry.getCustomProperties());
    }

    @Test
    public void testMergeCustomProperties_MatchingKeys_LatestValuePreserved() throws Exception {
        // Arrange
        SprinklrTicketEntry newEntry = createSprinklrTicketEntry();
        SprinklrTicketEntry existingEntry = createSprinklrTicketEntry();
        
        Map<String, List<Object>> newProperties = new HashMap<>();
        newProperties.put("status", Arrays.asList("LATEST_STATUS"));
        newProperties.put("priority", Arrays.asList("HIGH"));
        
        Map<String, List<Object>> existingProperties = new HashMap<>();
        existingProperties.put("status", Arrays.asList("OLD_STATUS"));
        existingProperties.put("assignee", Arrays.asList("john.doe"));
        
        newEntry.setCustomProperties(newProperties);
        existingEntry.setCustomProperties(existingProperties);
        
        // Act
        mergeCustomPropertiesMethod.invoke(sprinklrTicketService, newEntry, existingEntry);
        
        // Assert
        assertEquals(Arrays.asList("LATEST_STATUS"), newEntry.getCustomProperties().get("status"));
        assertEquals(Arrays.asList("HIGH"), newEntry.getCustomProperties().get("priority"));
        assertEquals(Arrays.asList("john.doe"), newEntry.getCustomProperties().get("assignee"));
    }

    @Test
    public void testMergeCustomProperties_EmptyMaps() throws Exception {
        // Arrange
        SprinklrTicketEntry newEntry = createSprinklrTicketEntry();
        SprinklrTicketEntry existingEntry = createSprinklrTicketEntry();
        
        newEntry.setCustomProperties(new HashMap<>());
        existingEntry.setCustomProperties(new HashMap<>());
        
        // Act
        mergeCustomPropertiesMethod.invoke(sprinklrTicketService, newEntry, existingEntry);
        
        // Assert
        assertTrue(newEntry.getCustomProperties().isEmpty());
    }

    // Helper method
    private SprinklrTicketEntry createSprinklrTicketEntry() {
        return SprinklrTicketEntry.builder()
                .caseId("CASE123")
                .caseNumber("12345")
                .userId("user123")
                .build();
    }
}
